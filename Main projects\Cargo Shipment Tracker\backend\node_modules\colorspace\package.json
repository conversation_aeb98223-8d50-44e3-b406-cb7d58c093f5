{"name": "colorspace", "version": "1.1.4", "description": "Generate HEX colors for a given namespace.", "main": "index.js", "scripts": {"test": "mocha test.js"}, "keywords": ["namespace", "color", "hex", "colorize", "name", "space", "colorspace"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/3rd-Eden/colorspace/issues"}, "homepage": "https://github.com/3rd-Eden/colorspace", "repository": {"type": "git", "url": "https://github.com/3rd-Eden/colorspace"}, "dependencies": {"color": "^3.1.3", "text-hex": "1.0.x"}, "devDependencies": {"assume": "2.1.x", "mocha": "5.2.x", "pre-commit": "1.2.x"}}