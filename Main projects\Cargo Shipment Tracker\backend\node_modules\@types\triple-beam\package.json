{"name": "@types/triple-beam", "version": "1.3.5", "description": "TypeScript definitions for triple-beam", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/triple-beam", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "danw<PERSON><PERSON>", "url": "https://github.com/danwbyrne"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/triple-beam"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "aba808a8cd292b633d60f24f8ed117bf7f4f83771da677fe4d557c4e1ad3211b", "typeScriptVersion": "4.5"}