import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert, Platform } from 'react-native';
import NetInfo from '@react-native-community/netinfo';

// Constants
const STORAGE_KEY = '@photo_gallery_cache';
const FLICKR_API_URL = 'https://api.flickr.com/services/rest/?method=flickr.photos.getRecent&per_page=20&page=1&api_key=6f102c62f41998d151e5a1b48713cf13&format=json&nojsoncallback=1&extras=url_s';
const JSONPLACEHOLDER_API_URL = 'https://jsonplaceholder.typicode.com/photos';

// Types
export interface Photo {
  id: string;
  title: string;
  url_s: string;
  width_s: number;
  height_s: number;
  thumbnailUrl?: string;
  url?: string;
}

// Check network connectivity
export const checkConnection = async (): Promise<boolean> => {
  const netInfo = await NetInfo.fetch();
  return netInfo.isConnected !== null ? netInfo.isConnected : false;
};

// Fetch photos from Flickr API with fallback to JSONPlaceholder
export const fetchRecentPhotos = async (): Promise<Photo[]> => {
  try {
    const isConnected = await checkConnection();
    if (!isConnected) {
      console.log('Offline mode - loading cached photos');
      // If offline, get cached photos
      return await getCachedPhotos();
    }
    
    // Try Flickr API first
    try {
      const flickrResponse = await fetch(FLICKR_API_URL);
      
      if (!flickrResponse.ok) {
        throw new Error(`Flickr API response was not ok: ${flickrResponse.status}`);
      }
      
      const flickrData = await flickrResponse.json();
      
      if (flickrData && flickrData.photos && flickrData.photos.photo && flickrData.photos.photo.length > 0) {
        const photos: Photo[] = flickrData.photos.photo.map((photo: any) => ({
          id: photo.id,
          title: photo.title || 'Untitled Photo',
          url_s: photo.url_s || 'https://via.placeholder.com/150',
          width_s: parseInt(photo.width_s) || 150,
          height_s: parseInt(photo.height_s) || 150,
        }));
        
        // Check if the photos are different from cached photos
        const hasChanges = await hasNewPhotos(photos);
        
        if (hasChanges) {
          // Cache the fetched photos if there are changes
          await cachePhotos(photos);
        }
        
        return photos;
      } else {
        throw new Error('Invalid or empty response from Flickr API');
      }
    } catch (flickrError) {
      console.warn('Flickr API failed, falling back to JSONPlaceholder:', flickrError);
      // Fallback to JSONPlaceholder API
      const jsonResponse = await fetch(`${JSONPLACEHOLDER_API_URL}?_limit=20`);
      
      if (!jsonResponse.ok) {
        throw new Error(`JSONPlaceholder API response was not ok: ${jsonResponse.status}`);
      }
      
      const jsonData = await jsonResponse.json();
      
      // Transform JSONPlaceholder data to match our Photo interface
      const photos: Photo[] = jsonData.map((item: any) => ({
        id: item.id.toString(),
        title: item.title || 'Untitled Photo',
        url_s: item.thumbnailUrl || 'https://via.placeholder.com/150',
        width_s: 150,
        height_s: 150,
        url: item.url
      }));
      
      // Check if the photos are different from cached photos
      const hasChanges = await hasNewPhotos(photos);
      
      if (hasChanges) {
        // Cache the fetched photos if there are changes
        await cachePhotos(photos);
      }
      
      return photos;
    }
  } catch (error) {
    console.error('Error fetching photos:', error);
    // If fetch fails, try to get cached photos
    return await getCachedPhotos();
  }
};

// Cache photos to AsyncStorage
export const cachePhotos = async (photos: Photo[]): Promise<void> => {
  try {
    await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(photos));
  } catch (error) {
    console.error('Error caching photos:', error);
  }
};

// Get cached photos from AsyncStorage
export const getCachedPhotos = async (): Promise<Photo[]> => {
  try {
    const cachedData = await AsyncStorage.getItem(STORAGE_KEY);
    if (cachedData) {
      return JSON.parse(cachedData) as Photo[];
    }
    return [];
  } catch (error) {
    console.error('Error getting cached photos:', error);
    return [];
  }
};

// Check if we have new photos compared to the cached ones
export const hasNewPhotos = async (newPhotos: Photo[]): Promise<boolean> => {
  try {
    const cachedPhotos = await getCachedPhotos();
    
    // If cache is empty, we definitely have new photos
    if (cachedPhotos.length === 0) return true;
    
    // If counts differ, we have new photos
    if (newPhotos.length !== cachedPhotos.length) return true;
    
    // Compare IDs to see if there are any differences
    const cachedIds = new Set(cachedPhotos.map(photo => photo.id));
    return newPhotos.some(photo => !cachedIds.has(photo.id));
  } catch (error) {
    console.error('Error comparing photos:', error);
    return false;
  }
};