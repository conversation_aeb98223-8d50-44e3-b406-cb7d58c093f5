<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Listings - EmptyCup Property Listings</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <a href="index.html">
                    <h1>EmptyCup</h1>
                    <p>Property Listings</p>
                </a>
            </div>
            <nav>
                <ul>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="listings.html" class="active">Listings</a></li>
                    <li><a href="about.html">About</a></li>
                    <li><a href="contact.html">Contact</a></li>
                    <li><a href="login.html">Login</a></li>
                    <li><a href="signup.html">Sign Up</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <section class="page-header">
        <div class="container">
            <h2>All Property Listings</h2>
        </div>
    </section>

    <section class="filters">
        <div class="container">
            <div class="filter-group search-group">
                <label for="search-input">Search:</label>
                <div class="search-container">
                    <input type="text" id="search-input" placeholder="Search by location, title, etc.">
                    <button id="search-button"><i class="fas fa-search"></i></button>
                </div>
            </div>
            <div class="filter-group">
                <label for="price-filter">Price Range:</label>
                <select id="price-filter">
                    <option value="all">All Prices</option>
                    <option value="0-1000">$0 - $1,000</option>
                    <option value="1000-2000">$1,000 - $2,000</option>
                    <option value="2000-3000">$2,000 - $3,000</option>
                    <option value="3000+">$3,000+</option>
                </select>
            </div>
            <div class="filter-group">
                <label for="bedrooms-filter">Bedrooms:</label>
                <select id="bedrooms-filter">
                    <option value="all">Any</option>
                    <option value="0">Studio</option>
                    <option value="1">1+</option>
                    <option value="2">2+</option>
                    <option value="3">3+</option>
                    <option value="4">4+</option>
                </select>
            </div>
            <div class="filter-group">
                <label for="sort-filter">Sort By:</label>
                <select id="sort-filter">
                    <option value="price-asc">Price: Low to High</option>
                    <option value="price-desc">Price: High to Low</option>
                    <option value="bedrooms">Bedrooms</option>
                    <option value="area">Area</option>
                </select>
            </div>
        </div>
    </section>

    <section class="listings">
        <div class="container">
            <div id="listings-container" class="listings-grid">
                <!-- Listings will be dynamically inserted here -->
                <div class="loading">Loading properties...</div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>EmptyCup</h3>
                    <p>Finding your perfect home made easy.</p>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="listings.html">Listings</a></li>
                        <li><a href="about.html">About</a></li>
                        <li><a href="contact.html">Contact</a></li>
                        <li><a href="login.html">Login</a></li>
                        <li><a href="signup.html">Sign Up</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Contact Us</h3>
                    <p>Email: <EMAIL></p>
                    <p>Phone: (*************</p>
                </div>
            </div>
            <div class="copyright">
                <p>&copy; 2025 EmptyCup. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>