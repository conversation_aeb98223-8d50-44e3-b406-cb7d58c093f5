/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

a {
    text-decoration: none;
    color: #333;
}

ul {
    list-style: none;
}

/* Header Styles */
header {
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 15px 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo h1 {
    color: #4a6ee0;
    font-size: 1.8rem;
    margin-bottom: 5px;
}

.logo p {
    font-size: 0.9rem;
    color: #777;
}

nav ul {
    display: flex;
}

nav ul li {
    margin-left: 20px;
}

nav ul li a {
    padding: 8px 12px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

nav ul li a:hover, nav ul li a.active {
    background-color: #4a6ee0;
    color: #fff;
}

/* Hero Section */
.hero {
    background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OHx8cmVhbCUyMGVzdGF0ZXxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=1200&q=60');
    background-size: cover;
    background-position: center;
    color: #fff;
    padding: 80px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(74, 110, 224, 0.5), rgba(22, 28, 51, 0.7));
    animation: gradientAnimation 10s ease infinite;
}

@keyframes gradientAnimation {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

.hero .container {
    position: relative;
    z-index: 1;
}

.hero h2 {
    font-size: 2.5rem;
    margin-bottom: 15px;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
    animation: fadeInDown 1s ease-out;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
    animation: fadeInDown 1s ease-out 0.2s both;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-top: 40px;
    animation: fadeInUp 1s ease-out 0.6s both;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: white;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 5px;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

.stat-label {
    font-size: 1rem;
    opacity: 0.9;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.search-container {
    display: flex;
    max-width: 600px;
    margin: 0 auto;
    animation: fadeInUp 1s ease-out 0.3s both;
    position: relative;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.search-container input {
    flex: 1;
    padding: 15px;
    border: none;
    border-radius: 4px 0 0 4px;
    font-size: 1rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.search-container input:focus {
    box-shadow: 0 5px 20px rgba(74, 110, 224, 0.4);
    outline: none;
}

.search-container button {
    padding: 15px 20px;
    background-color: #4a6ee0;
    color: #fff;
    border: none;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.search-container button:hover {
    background-color: #3a5bc7;
    transform: translateY(-2px);
    box-shadow: 0 7px 20px rgba(0, 0, 0, 0.3);
}

/* Filters Section */
.filters {
    background-color: #fff;
    padding: 20px 0;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.filters .container {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}

.filter-group {
    margin: 10px 0;
}

.filter-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.filter-group select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
    min-width: 150px;
}

.search-group {
    flex-grow: 1;
    max-width: 400px;
}

.search-container {
    display: flex;
    align-items: center;
}

#search-input {
    flex-grow: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px 0 0 4px;
    border-right: none;
}

#search-button {
    background-color: #4a6ee0;
    color: white;
    border: none;
    padding: 9px 15px;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

#search-button:hover {
    background-color: #3a5bc0;
}

/* Listings Section */
.listings {
    padding: 50px 0;
}

.listings h2, .shortlist h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
    font-size: 2rem;
}

.listings-grid, .shortlist-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
}

.property-card {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    animation: fadeIn 0.6s ease-out;
    opacity: 0;
    animation-fill-mode: forwards;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.property-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.property-card:nth-child(1) { animation-delay: 0.1s; }
.property-card:nth-child(2) { animation-delay: 0.2s; }
.property-card:nth-child(3) { animation-delay: 0.3s; }
.property-card:nth-child(4) { animation-delay: 0.4s; }
.property-card:nth-child(5) { animation-delay: 0.5s; }
.property-card:nth-child(6) { animation-delay: 0.6s; }

.property-image {
    height: 200px;
    background-size: cover;
    background-position: center;
    position: relative;
}

.property-price {
    position: absolute;
    bottom: 0;
    left: 0;
    background-color: rgba(74, 110, 224, 0.9);
    color: #fff;
    padding: 8px 15px;
    font-weight: 600;
    border-radius: 0 4px 0 0;
}

.shortlist-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: rgba(255, 255, 255, 0.8);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.shortlist-btn:hover {
    background-color: #fff;
}

.shortlist-btn i {
    color: #ccc;
    font-size: 1.2rem;
    transition: color 0.3s ease;
}

.shortlist-btn.active i {
    color: #e74c3c;
}

.property-details {
    padding: 20px;
}

.property-title {
    font-size: 1.2rem;
    margin-bottom: 10px;
    color: #333;
}

.property-location {
    color: #777;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.property-location i {
    margin-right: 5px;
    color: #4a6ee0;
}

.property-features {
    display: flex;
    justify-content: space-between;
    border-top: 1px solid #eee;
    padding-top: 15px;
}

.feature {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.feature span {
    font-weight: 600;
    margin-bottom: 5px;
}

.feature small {
    color: #777;
}

.property-actions {
    display: flex;
    justify-content: space-between;
    padding: 15px 20px;
    border-top: 1px solid #eee;
    margin-top: 10px;
}

.btn-like, .btn-book {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 15px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-like {
    background-color: #f8f9fa;
    color: #333;
    border: 1px solid #ddd;
}

.btn-like.active {
    background-color: #ffebee;
    color: #e53935;
    border-color: #ffcdd2;
}

.btn-like:hover {
    background-color: #f1f1f1;
}

.btn-like.active:hover {
    background-color: #ffe6e6;
}

.btn-book {
    background-color: #4a6ee0;
    color: white;
}

.btn-book:hover {
    background-color: #3a5bc7;
    transform: translateY(-2px);
}

.btn-like i, .btn-book i {
    margin-right: 5px;
}

.loading {
    grid-column: 1 / -1;
    text-align: center;
    padding: 30px;
    font-size: 1.2rem;
    color: #777;
}

.empty-shortlist {
    grid-column: 1 / -1;
    text-align: center;
    padding: 30px;
    color: #777;
}

/* Shortlist Section */
.shortlist {
    padding: 50px 0;
    background-color: #f0f4ff;
}

/* Footer */
footer {
    background-color: #333;
    color: #fff;
    padding: 50px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.footer-section h3 {
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul li a {
    color: #ddd;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #4a6ee0;
}

.copyright {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #444;
    color: #aaa;
    font-size: 0.9rem;
}

/* Page Header */
.page-header {
    background-color: #4a6ee0;
    color: #fff;
    padding: 40px 0;
    text-align: center;
}

.page-header h2 {
    font-size: 2.2rem;
    margin: 0;
}

/* About Page Styles */
.about-content {
    padding: 50px 0;
}

.about-section {
    margin-bottom: 40px;
}

.about-section h3 {
    color: #4a6ee0;
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.feature-list {
    padding-left: 20px;
}

.feature-list li {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.feature-list li i {
    color: #4a6ee0;
    margin-right: 10px;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 30px;
    margin-top: 20px;
}

.team-member {
    text-align: center;
}

.team-photo {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    margin: 0 auto 15px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.team-photo i {
    font-size: 3rem;
    color: #777;
}

.team-member h4 {
    margin-bottom: 5px;
    color: #333;
}

.team-member p {
    color: #777;
}

/* Contact Page Styles */
.contact-content {
    padding: 50px 0;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
}

.contact-method {
    display: flex;
    margin-bottom: 30px;
}

.contact-method i {
    font-size: 2rem;
    color: #4a6ee0;
    margin-right: 20px;
}

.contact-method h3 {
    margin-bottom: 10px;
    color: #333;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: inherit;
}

.btn-primary {
    background-color: #4a6ee0;
    color: #fff;
    border: none;
    padding: 12px 25px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.btn-primary:hover {
    background-color: #3a5bc7;
}

#form-response {
    margin-top: 20px;
    padding: 15px;
    border-radius: 4px;
}

#form-response.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

#form-response.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.hidden {
    display: none;
}

/* Authentication Styles */
.auth-content {
    padding: 50px 0;
    background-color: #f8f9fa;
}

.auth-form {
    max-width: 500px;
    margin: 0 auto;
    background-color: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.auth-links {
    margin-top: 20px;
    text-align: center;
}

.auth-links a {
    color: #4a6ee0;
    text-decoration: none;
    transition: color 0.3s ease;
}

.auth-links a:hover {
    text-decoration: underline;
}

.auth-links p {
    margin-bottom: 10px;
}

/* Social Login Styles */
.social-login {
    margin: 20px 0;
    text-align: center;
}

.social-login p {
    margin-bottom: 15px;
    position: relative;
}

.social-login p:before,
.social-login p:after {
    content: "";
    display: inline-block;
    width: 30%;
    height: 1px;
    background: #ddd;
    vertical-align: middle;
    margin: 0 10px;
}

.social-buttons {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.btn-social {
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    color: white;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.btn-social i {
    margin-right: 8px;
}

.btn-google {
    background-color: #DB4437;
}

.btn-facebook {
    background-color: #4267B2;
}

.btn-apple {
    background-color: #000;
}

.btn-social:hover {
    opacity: 0.9;
    transform: translateY(-2px);
}

/* Logo Link Styles */
.logo a {
    text-decoration: none;
    display: block;
}

/* Responsive Styles */
@media (max-width: 768px) {
    header .container {
        flex-direction: column;
    }
    
    nav ul {
        margin-top: 15px;
    }
    
    .filters .container {
        flex-direction: column;
    }
    
    .filter-group {
        width: 100%;
    }
    
    .filter-group select {
        width: 100%;
    }
    
    .hero h2 {
        font-size: 2rem;
    }
    
    .contact-content {
        grid-template-columns: 1fr;
    }
    
    .team-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}

@media (max-width: 576px) {
    nav ul {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    nav ul li {
        margin: 5px;
    }
    
    .search-container {
        flex-direction: column;
    }
    
    .search-container input, .search-container button {
        border-radius: 4px;
        width: 100%;
    }
    
    .search-container button {
        margin-top: 10px;
    }
}