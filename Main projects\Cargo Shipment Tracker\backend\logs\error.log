{"error":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\express-mongo-sanitize\\index.js:113:18\n    at Array.forEach (<anonymous>)\n    at D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\express-mongo-sanitize\\index.js:110:44\n    at Layer.handleRequest (D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\router\\index.js:342:13)\n    at D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\router\\index.js:582:12)\n    at next (D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\router\\lib\\layer.js:152:17)","ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Cannot set property query of #<IncomingMessage> which has only a getter\u001b[39m","method":"GET","timestamp":"2025-07-11 15:28:47:2847","url":"/api","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Cannot set property query of #<IncomingMessage> which has only a getter","ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mUnhandled error:\u001b[39m","method":"GET","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\express-mongo-sanitize\\index.js:113:18\n    at Array.forEach (<anonymous>)\n    at D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\express-mongo-sanitize\\index.js:110:44\n    at Layer.handleRequest (D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\router\\index.js:342:13)\n    at D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\router\\index.js:582:12)\n    at next (D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\router\\lib\\layer.js:152:17)","timestamp":"2025-07-11 15:28:47:2847","url":"/api"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mGET /api - 500 - 113ms - ::1\u001b[39m","timestamp":"2025-07-11 15:28:47:2847"}
{"error":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\express-mongo-sanitize\\index.js:113:18\n    at Array.forEach (<anonymous>)\n    at D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\express-mongo-sanitize\\index.js:110:44\n    at Layer.handleRequest (D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\router\\index.js:342:13)\n    at D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\router\\index.js:582:12)\n    at next (D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\router\\lib\\layer.js:152:17)","ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Cannot set property query of #<IncomingMessage> which has only a getter\u001b[39m","method":"GET","timestamp":"2025-07-11 15:28:48:2848","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Cannot set property query of #<IncomingMessage> which has only a getter","ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mUnhandled error:\u001b[39m","method":"GET","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\express-mongo-sanitize\\index.js:113:18\n    at Array.forEach (<anonymous>)\n    at D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\express-mongo-sanitize\\index.js:110:44\n    at Layer.handleRequest (D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\router\\index.js:342:13)\n    at D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\router\\index.js:297:9\n    at processParams (D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\router\\index.js:582:12)\n    at next (D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\router\\lib\\layer.js:152:17)","timestamp":"2025-07-11 15:28:48:2848","url":"/favicon.ico"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mGET /favicon.ico - 500 - 54ms - ::1\u001b[39m","timestamp":"2025-07-11 15:28:48:2848"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mServer error: listen EADDRINUSE: address already in use :::5000\u001b[39m","port":5000,"stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\node_modules\\express\\lib\\application.js:605:24)\n    at Object.<anonymous> (D:\\Delete folder-C\\web develpment\\Main projects\\Cargo Shipment Tracker\\backend\\server.js:753:20)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","syscall":"listen","timestamp":"2025-07-11 16:22:47:2247"}
