# Educase-ReactJS Assignment

This project is a qualifier task for the React.js internship at **Educase**. It replicates a mobile app design using **React with Vite**, **Tailwind CSS**, and is hosted on **Render**.

## 🔗 Links

- **Live Demo:** [Hosted on Render](https://pop-x-nckj.onrender.com)
- **Repository:** [GitHub Repo](https://github.com/Inayat-hasan/Educase-ReactJs)
- **Design Reference:** [Adobe XD Design](https://xd.adobe.com/view/b68eea25-003d-4a5d-8fdd-d463eeb20b32-e3dd)

---

## ✨ Features

- 💻 Pixel-perfect implementation based on the provided Adobe XD design
- 📱 Responsive and centered mobile interface
- 🔄 Seamless navigation between pages using React Router
- ⚡ Fast build and development using Vite
- 🎨 Styled with Tailwind CSS for utility-first, mobile-friendly design

---

## 📦 Tech Stack

- **Framework:** React (via Vite)
- **Styling:** Tailwind CSS
- **Routing:** React Router
- **Hosting:** Render

---

## 📁 Getting Started

To run the project locally:

```bash
# Clone the repository
git clone https://github.com/Inayat-hasan/Educase-ReactJs.git
cd educase-project

# Install dependencies
npm install

# Start the development server
npm run dev
```
