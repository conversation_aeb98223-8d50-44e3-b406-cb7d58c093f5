# EmptyCup Property Listings Application

A comprehensive web application for browsing, filtering, and shortlisting property listings, built with a Flask backend and vanilla JavaScript frontend. This application provides a responsive user interface with multiple pages, authentication features, and interactive property management.

![EmptyCup Property Listings](https://via.placeholder.com/800x400?text=EmptyCup+Property+Listings)

## Table of Contents

- [Features](#features)
- [Project Structure](#project-structure)
- [Technologies Used](#technologies-used)
- [Installation](#installation)
  - [Prerequisites](#prerequisites)
  - [Option 1: Running with Docker (Recommended)](#option-1-running-with-docker-recommended)
  - [Option 2: Running Locally](#option-2-running-locally)
- [Usage Guide](#usage-guide)
  - [Navigation](#navigation)
  - [Property Browsing](#property-browsing)
  - [Filtering and Searching](#filtering-and-searching)
  - [User Authentication](#user-authentication)
  - [Shortlisting Properties](#shortlisting-properties)
- [API Documentation](#api-documentation)
- [Development](#development)
  - [Backend](#backend)
  - [Frontend](#frontend)
- [Deployment](#deployment)
- [Testing](#testing)
- [Troubleshooting](#troubleshooting)
- [Contributing](#contributing)
- [License](#license)
- [Contact](#contact)

## Features

### Core Features
- Browse property listings with detailed information and high-quality images
- Filter properties by:
  - Price range (e.g., $1,000-$2,000, All Prices)
  - Number of bedrooms (Studio, 1, 2+, Any)
  - Location (search by neighborhood, city, etc.)
- Sort properties by different criteria (price, date added, etc.)
- Shortlist favorite properties for later viewing
- Responsive design optimized for mobile, tablet, and desktop devices

### User Interface
- Modern, clean design following the Figma specifications
- Animated elements for enhanced user experience:
  - Gradient animations on the home page
  - FadeIn/FadeUp effects for content sections
  - Staggered loading animations for property cards
  - Animated statistics counters in the hero section
- Interactive property cards with hover effects
- Like and book buttons for quick property actions

### Navigation
- Multiple pages with working navigation:
  - Home page with featured listings and statistics
  - Listings page with all properties and filtering options
  - About page with company information
  - Contact page with contact form and details
  - Login page for user authentication
  - Sign Up page for new user registration
- Clickable logo for easy navigation to home page
- Active state indicators in navigation menu

### Authentication
- User login and registration system (simulated with localStorage)
- Social login options (Google, Facebook, Apple)
- Persistent user sessions across page navigation
- Protected features (e.g., shortlisting requires login)

## Project Structure

```
EmptyCupAssignment/
├── backend/
│   ├── app.py                # Flask application with API endpoints
│   └── listings.json         # Property data in JSON format
├── frontend/
│   ├── index.html           # Home page with featured listings
│   ├── listings.html        # Listings page with filtering options
│   ├── about.html           # About page with company information
│   ├── contact.html         # Contact page with form
│   ├── login.html           # Login page with social options
│   ├── signup.html          # Sign Up page with registration form
│   ├── styles.css           # CSS styling with responsive design
│   └── script.js            # JavaScript functionality
├── docker-compose.yml       # Docker Compose configuration
├── Dockerfile               # Docker container configuration
├── requirements.txt         # Python dependencies
└── README.md                # Project documentation
```

## Technologies Used

### Backend
- **Flask**: Lightweight Python web framework
- **Flask-CORS**: Cross-Origin Resource Sharing support
- **JSON**: Data storage format for property listings
- **Werkzeug**: WSGI web application library

### Frontend
- **HTML5**: Structure and content
- **CSS3**: Styling with flexbox and grid layouts
- **JavaScript (ES6+)**: Client-side functionality
- **LocalStorage API**: Client-side data persistence
- **Fetch API**: Asynchronous HTTP requests

### DevOps
- **Docker**: Containerization
- **Docker Compose**: Multi-container orchestration

## Installation

### Prerequisites

- **Docker and Docker Compose** (for containerized deployment)
- **Alternatively**: Python 3.9+ and a modern web browser
- **Git** (for cloning the repository)

### Option 1: Running with Docker (Recommended)

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd EmptyCupAssignment
   ```

2. **Build and start the Docker containers**:
   ```bash
   docker-compose up --build
   ```

3. **Access the application** in your browser:
   ```
   http://localhost:12001
   ```

### Option 2: Running Locally

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd EmptyCupAssignment
   ```

2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Start the Flask backend**:
   ```bash
   cd backend
   python app.py
   ```

4. **Access the application** in your browser:
   ```
   http://localhost:12001
   ```

## Usage Guide

### Navigation

- **Home Page**: Access via the EmptyCup logo or "Home" link in the navigation menu
- **Listings**: View all properties with filtering options
- **About**: Learn about EmptyCup and its services
- **Contact**: Get in touch with the EmptyCup team
- **Login/Signup**: Access user authentication features

### Property Browsing

1. Visit the **Listings** page to view all available properties
2. Click on property cards to view more details
3. Use the "Like" button to mark properties of interest
4. Use the "Book" button to schedule a viewing

### Filtering and Searching

1. **Price Range**: Select from dropdown (e.g., $1,000-$2,000, All Prices)
2. **Bedrooms**: Choose from options (Studio, 1, 2+, Any)
3. **Search**: Enter location or keywords in the search box
4. **Combined Filtering**: Apply multiple filters simultaneously for refined results

### User Authentication

1. **Login**:
   - Use email/password: <EMAIL> / password123
   - Or click on social login options (Google, Facebook, Apple)
   
2. **Sign Up**:
   - Enter name, email, and password
   - Or use social signup options

### Shortlisting Properties

1. **Login** to your account
2. Click the **heart icon** on property cards to add to shortlist
3. Click again to remove from shortlist
4. Shortlisted properties are saved to your account

## API Documentation

### Endpoints

- **GET /api/listings**
  - Returns all property listings
  - No parameters required
  - Response: JSON array of property objects

- **GET /api/listings/<id>**
  - Returns a specific property by ID
  - Parameters: id (property identifier)
  - Response: JSON object with property details

### Sample Response

```json
{
  "id": 1,
  "title": "Modern Downtown Apartment",
  "location": "Downtown, New York",
  "price": 1500,
  "bedrooms": 1,
  "bathrooms": 1,
  "area": 750,
  "image": "https://example.com/property1.jpg",
  "description": "Beautiful modern apartment in the heart of downtown...",
  "amenities": ["Air Conditioning", "Gym", "Parking"]
}
```

## Development

### Backend

The backend is built with Flask and provides RESTful API endpoints:

- **app.py**: Main Flask application with route definitions
- **listings.json**: Sample property data in JSON format

To modify the backend:
1. Edit `app.py` to add new routes or modify existing ones
2. Update `listings.json` to change property data
3. Restart the server to apply changes

### Frontend

The frontend is built with vanilla HTML, CSS, and JavaScript:

- **HTML Pages**: Structure and content for each page
- **styles.css**: Styling with responsive design
- **script.js**: JavaScript functionality including:
  - API integration
  - DOM manipulation
  - Event handling
  - Filtering and sorting logic
  - Authentication management
  - Local storage operations

To modify the frontend:
1. Edit HTML files to change page structure
2. Update styles.css for visual changes
3. Modify script.js for behavioral changes

## Deployment

The application can be deployed to any platform that supports Docker containers:

1. **Cloud Platforms**:
   - AWS Elastic Container Service (ECS)
   - Google Cloud Run
   - Azure Container Instances
   - Heroku (with Docker support)

2. **Self-Hosted**:
   - Kubernetes cluster
   - Docker Swarm
   - Single server with Docker

## Testing

### Manual Testing

1. **Navigation Testing**:
   - Verify all navigation links work correctly
   - Check active states in navigation menu
   - Test logo navigation to home page

2. **Functionality Testing**:
   - Test property filtering (price, bedrooms, location)
   - Verify search functionality
   - Test shortlisting (add/remove properties)
   - Verify authentication (login/signup)

3. **Responsive Design Testing**:
   - Test on multiple device sizes (mobile, tablet, desktop)
   - Verify layout adjusts appropriately

### Test Accounts

- **Email/Password**: <EMAIL> / password123
- **New User**: Create an account with signup form

## Troubleshooting

### Common Issues

1. **Application not starting**:
   - Verify Docker is running
   - Check port 12001 is not in use by another application
   - Ensure all dependencies are installed

2. **API not responding**:
   - Check backend logs for errors
   - Verify CORS settings if accessing from different domain

3. **Login issues**:
   - Clear browser localStorage and try again
   - Check browser console for JavaScript errors

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Commit your changes: `git commit -m 'Add some feature'`
4. Push to the branch: `git push origin feature-name`
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contact

For questions or support, please contact:
- Email: <EMAIL>
- Website: https://www.emptycup.com
- GitHub: [EmptyCup GitHub Organization](https://github.com/emptycup)