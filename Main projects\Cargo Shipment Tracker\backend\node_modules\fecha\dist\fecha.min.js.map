{"version": 3, "file": "fecha.min.js", "sources": ["../src/fecha.ts"], "sourcesContent": ["const token = /d{1,4}|M{1,4}|YY(?:YY)?|S{1,3}|Do|ZZ|Z|([HhMsDm])\\1?|[aA]|\"[^\"]*\"|'[^']*'/g;\nconst twoDigitsOptional = \"\\\\d\\\\d?\";\nconst twoDigits = \"\\\\d\\\\d\";\nconst threeDigits = \"\\\\d{3}\";\nconst fourDigits = \"\\\\d{4}\";\nconst word = \"[^\\\\s]+\";\nconst literal = /\\[([^]*?)\\]/gm;\n\ntype DateInfo = {\n  year: number;\n  month: number;\n  day: number;\n  hour: number;\n  minute: number;\n  second: number;\n  millisecond: number;\n  isPm: number | null;\n  timezoneOffset: number | null;\n};\n\nexport type I18nSettings = {\n  amPm: [string, string];\n  dayNames: Days;\n  dayNamesShort: Days;\n  monthNames: Months;\n  monthNamesShort: Months;\n  DoFn(dayOfMonth: number): string;\n};\n\nexport type I18nSettingsOptional = Partial<I18nSettings>;\n\nexport type Days = [string, string, string, string, string, string, string];\nexport type Months = [\n  string,\n  string,\n  string,\n  string,\n  string,\n  string,\n  string,\n  string,\n  string,\n  string,\n  string,\n  string\n];\n\nfunction shorten<T extends string[]>(arr: T, sLen: number): string[] {\n  const newArr: string[] = [];\n  for (let i = 0, len = arr.length; i < len; i++) {\n    newArr.push(arr[i].substr(0, sLen));\n  }\n  return newArr;\n}\n\nconst monthUpdate = (\n  arrName: \"monthNames\" | \"monthNamesShort\" | \"dayNames\" | \"dayNamesShort\"\n) => (v: string, i18n: I18nSettings): number | null => {\n  const lowerCaseArr = i18n[arrName].map(v => v.toLowerCase());\n  const index = lowerCaseArr.indexOf(v.toLowerCase());\n  if (index > -1) {\n    return index;\n  }\n  return null;\n};\n\nexport function assign<A>(a: A): A;\nexport function assign<A, B>(a: A, b: B): A & B;\nexport function assign<A, B, C>(a: A, b: B, c: C): A & B & C;\nexport function assign<A, B, C, D>(a: A, b: B, c: C, d: D): A & B & C & D;\nexport function assign(origObj: any, ...args: any[]): any {\n  for (const obj of args) {\n    for (const key in obj) {\n      // @ts-ignore ex\n      origObj[key] = obj[key];\n    }\n  }\n  return origObj;\n}\n\nconst dayNames: Days = [\n  \"Sunday\",\n  \"Monday\",\n  \"Tuesday\",\n  \"Wednesday\",\n  \"Thursday\",\n  \"Friday\",\n  \"Saturday\"\n];\nconst monthNames: Months = [\n  \"January\",\n  \"February\",\n  \"March\",\n  \"April\",\n  \"May\",\n  \"June\",\n  \"July\",\n  \"August\",\n  \"September\",\n  \"October\",\n  \"November\",\n  \"December\"\n];\n\nconst monthNamesShort: Months = shorten(monthNames, 3) as Months;\nconst dayNamesShort: Days = shorten(dayNames, 3) as Days;\n\nconst defaultI18n: I18nSettings = {\n  dayNamesShort,\n  dayNames,\n  monthNamesShort,\n  monthNames,\n  amPm: [\"am\", \"pm\"],\n  DoFn(dayOfMonth: number) {\n    return (\n      dayOfMonth +\n      [\"th\", \"st\", \"nd\", \"rd\"][\n        dayOfMonth % 10 > 3\n          ? 0\n          : ((dayOfMonth - (dayOfMonth % 10) !== 10 ? 1 : 0) * dayOfMonth) % 10\n      ]\n    );\n  }\n};\nlet globalI18n = assign({}, defaultI18n);\nconst setGlobalDateI18n = (i18n: I18nSettingsOptional): I18nSettings =>\n  (globalI18n = assign(globalI18n, i18n));\n\nconst regexEscape = (str: string): string =>\n  str.replace(/[|\\\\{()[^$+*?.-]/g, \"\\\\$&\");\n\nconst pad = (val: string | number, len = 2): string => {\n  val = String(val);\n  while (val.length < len) {\n    val = \"0\" + val;\n  }\n  return val;\n};\n\nconst formatFlags: Record<\n  string,\n  (dateObj: Date, i18n: I18nSettings) => string\n> = {\n  D: (dateObj: Date): string => String(dateObj.getDate()),\n  DD: (dateObj: Date): string => pad(dateObj.getDate()),\n  Do: (dateObj: Date, i18n: I18nSettings): string =>\n    i18n.DoFn(dateObj.getDate()),\n  d: (dateObj: Date): string => String(dateObj.getDay()),\n  dd: (dateObj: Date): string => pad(dateObj.getDay()),\n  ddd: (dateObj: Date, i18n: I18nSettings): string =>\n    i18n.dayNamesShort[dateObj.getDay()],\n  dddd: (dateObj: Date, i18n: I18nSettings): string =>\n    i18n.dayNames[dateObj.getDay()],\n  M: (dateObj: Date): string => String(dateObj.getMonth() + 1),\n  MM: (dateObj: Date): string => pad(dateObj.getMonth() + 1),\n  MMM: (dateObj: Date, i18n: I18nSettings): string =>\n    i18n.monthNamesShort[dateObj.getMonth()],\n  MMMM: (dateObj: Date, i18n: I18nSettings): string =>\n    i18n.monthNames[dateObj.getMonth()],\n  YY: (dateObj: Date): string =>\n    pad(String(dateObj.getFullYear()), 4).substr(2),\n  YYYY: (dateObj: Date): string => pad(dateObj.getFullYear(), 4),\n  h: (dateObj: Date): string => String(dateObj.getHours() % 12 || 12),\n  hh: (dateObj: Date): string => pad(dateObj.getHours() % 12 || 12),\n  H: (dateObj: Date): string => String(dateObj.getHours()),\n  HH: (dateObj: Date): string => pad(dateObj.getHours()),\n  m: (dateObj: Date): string => String(dateObj.getMinutes()),\n  mm: (dateObj: Date): string => pad(dateObj.getMinutes()),\n  s: (dateObj: Date): string => String(dateObj.getSeconds()),\n  ss: (dateObj: Date): string => pad(dateObj.getSeconds()),\n  S: (dateObj: Date): string =>\n    String(Math.round(dateObj.getMilliseconds() / 100)),\n  SS: (dateObj: Date): string =>\n    pad(Math.round(dateObj.getMilliseconds() / 10), 2),\n  SSS: (dateObj: Date): string => pad(dateObj.getMilliseconds(), 3),\n  a: (dateObj: Date, i18n: I18nSettings): string =>\n    dateObj.getHours() < 12 ? i18n.amPm[0] : i18n.amPm[1],\n  A: (dateObj: Date, i18n: I18nSettings): string =>\n    dateObj.getHours() < 12\n      ? i18n.amPm[0].toUpperCase()\n      : i18n.amPm[1].toUpperCase(),\n  ZZ(dateObj: Date): string {\n    const offset = dateObj.getTimezoneOffset();\n    return (\n      (offset > 0 ? \"-\" : \"+\") +\n      pad(Math.floor(Math.abs(offset) / 60) * 100 + (Math.abs(offset) % 60), 4)\n    );\n  },\n  Z(dateObj: Date): string {\n    const offset = dateObj.getTimezoneOffset();\n    return (\n      (offset > 0 ? \"-\" : \"+\") +\n      pad(Math.floor(Math.abs(offset) / 60), 2) +\n      \":\" +\n      pad(Math.abs(offset) % 60, 2)\n    );\n  }\n};\n\ntype ParseInfo = [\n  keyof DateInfo,\n  string,\n  ((v: string, i18n: I18nSettings) => number | null)?,\n  string?\n];\nconst monthParse = (v: string): number => +v - 1;\nconst emptyDigits: ParseInfo = [null, twoDigitsOptional];\nconst emptyWord: ParseInfo = [null, word];\nconst amPm: ParseInfo = [\n  \"isPm\",\n  word,\n  (v: string, i18n: I18nSettings): number | null => {\n    const val = v.toLowerCase();\n    if (val === i18n.amPm[0]) {\n      return 0;\n    } else if (val === i18n.amPm[1]) {\n      return 1;\n    }\n    return null;\n  }\n];\nconst timezoneOffset: ParseInfo = [\n  \"timezoneOffset\",\n  \"[^\\\\s]*?[\\\\+\\\\-]\\\\d\\\\d:?\\\\d\\\\d|[^\\\\s]*?Z?\",\n  (v: string): number | null => {\n    const parts = (v + \"\").match(/([+-]|\\d\\d)/gi);\n\n    if (parts) {\n      const minutes = +parts[1] * 60 + parseInt(parts[2], 10);\n      return parts[0] === \"+\" ? minutes : -minutes;\n    }\n\n    return 0;\n  }\n];\nconst parseFlags: Record<string, ParseInfo> = {\n  D: [\"day\", twoDigitsOptional],\n  DD: [\"day\", twoDigits],\n  Do: [\"day\", twoDigitsOptional + word, (v: string): number => parseInt(v, 10)],\n  M: [\"month\", twoDigitsOptional, monthParse],\n  MM: [\"month\", twoDigits, monthParse],\n  YY: [\n    \"year\",\n    twoDigits,\n    (v: string): number => {\n      const now = new Date();\n      const cent = +(\"\" + now.getFullYear()).substr(0, 2);\n      return +(\"\" + (+v > 68 ? cent - 1 : cent) + v);\n    }\n  ],\n  h: [\"hour\", twoDigitsOptional, undefined, \"isPm\"],\n  hh: [\"hour\", twoDigits, undefined, \"isPm\"],\n  H: [\"hour\", twoDigitsOptional],\n  HH: [\"hour\", twoDigits],\n  m: [\"minute\", twoDigitsOptional],\n  mm: [\"minute\", twoDigits],\n  s: [\"second\", twoDigitsOptional],\n  ss: [\"second\", twoDigits],\n  YYYY: [\"year\", fourDigits],\n  S: [\"millisecond\", \"\\\\d\", (v: string): number => +v * 100],\n  SS: [\"millisecond\", twoDigits, (v: string): number => +v * 10],\n  SSS: [\"millisecond\", threeDigits],\n  d: emptyDigits,\n  dd: emptyDigits,\n  ddd: emptyWord,\n  dddd: emptyWord,\n  MMM: [\"month\", word, monthUpdate(\"monthNamesShort\")],\n  MMMM: [\"month\", word, monthUpdate(\"monthNames\")],\n  a: amPm,\n  A: amPm,\n  ZZ: timezoneOffset,\n  Z: timezoneOffset\n};\n\n// Some common format strings\nconst globalMasks: { [key: string]: string } = {\n  default: \"ddd MMM DD YYYY HH:mm:ss\",\n  shortDate: \"M/D/YY\",\n  mediumDate: \"MMM D, YYYY\",\n  longDate: \"MMMM D, YYYY\",\n  fullDate: \"dddd, MMMM D, YYYY\",\n  isoDate: \"YYYY-MM-DD\",\n  isoDateTime: \"YYYY-MM-DDTHH:mm:ssZ\",\n  shortTime: \"HH:mm\",\n  mediumTime: \"HH:mm:ss\",\n  longTime: \"HH:mm:ss.SSS\"\n};\nconst setGlobalDateMasks = (masks: {\n  [key: string]: string;\n}): { [key: string]: string } => assign(globalMasks, masks);\n\n/***\n * Format a date\n * @method format\n * @param {Date|number} dateObj\n * @param {string} mask Format of the date, i.e. 'mm-dd-yy' or 'shortDate'\n * @returns {string} Formatted date string\n */\nconst format = (\n  dateObj: Date,\n  mask: string = globalMasks[\"default\"],\n  i18n: I18nSettingsOptional = {}\n): string => {\n  if (typeof dateObj === \"number\") {\n    dateObj = new Date(dateObj);\n  }\n\n  if (\n    Object.prototype.toString.call(dateObj) !== \"[object Date]\" ||\n    isNaN(dateObj.getTime())\n  ) {\n    throw new Error(\"Invalid Date pass to format\");\n  }\n\n  mask = globalMasks[mask] || mask;\n\n  const literals: string[] = [];\n\n  // Make literals inactive by replacing them with @@@\n  mask = mask.replace(literal, function($0, $1) {\n    literals.push($1);\n    return \"@@@\";\n  });\n\n  const combinedI18nSettings: I18nSettings = assign(\n    assign({}, globalI18n),\n    i18n\n  );\n  // Apply formatting rules\n  mask = mask.replace(token, $0 =>\n    formatFlags[$0](dateObj, combinedI18nSettings)\n  );\n  // Inline literal values back into the formatted value\n  return mask.replace(/@@@/g, () => literals.shift());\n};\n\n/**\n * Parse a date string into a Javascript Date object /\n * @method parse\n * @param {string} dateStr Date string\n * @param {string} format Date parse format\n * @param {i18n} I18nSettingsOptional Full or subset of I18N settings\n * @returns {Date|null} Returns Date object. Returns null what date string is invalid or doesn't match format\n */\nfunction parse(\n  dateStr: string,\n  format: string,\n  i18n: I18nSettingsOptional = {}\n): Date | null {\n  if (typeof format !== \"string\") {\n    throw new Error(\"Invalid format in fecha parse\");\n  }\n\n  // Check to see if the format is actually a mask\n  format = globalMasks[format] || format;\n\n  // Avoid regular expression denial of service, fail early for really long strings\n  // https://www.owasp.org/index.php/Regular_expression_Denial_of_Service_-_ReDoS\n  if (dateStr.length > 1000) {\n    return null;\n  }\n\n  // Default to the beginning of the year.\n  const today = new Date();\n  const dateInfo: DateInfo = {\n    year: today.getFullYear(),\n    month: 0,\n    day: 1,\n    hour: 0,\n    minute: 0,\n    second: 0,\n    millisecond: 0,\n    isPm: null,\n    timezoneOffset: null\n  };\n  const parseInfo: ParseInfo[] = [];\n  const literals: string[] = [];\n\n  // Replace all the literals with @@@. Hopefully a string that won't exist in the format\n  let newFormat = format.replace(literal, ($0, $1) => {\n    literals.push(regexEscape($1));\n    return \"@@@\";\n  });\n  const specifiedFields: { [field: string]: boolean } = {};\n  const requiredFields: { [field: string]: boolean } = {};\n\n  // Change every token that we find into the correct regex\n  newFormat = regexEscape(newFormat).replace(token, $0 => {\n    const info = parseFlags[$0];\n    const [field, regex, , requiredField] = info;\n\n    // Check if the person has specified the same field twice. This will lead to confusing results.\n    if (specifiedFields[field]) {\n      throw new Error(`Invalid format. ${field} specified twice in format`);\n    }\n\n    specifiedFields[field] = true;\n\n    // Check if there are any required fields. For instance, 12 hour time requires AM/PM specified\n    if (requiredField) {\n      requiredFields[requiredField] = true;\n    }\n\n    parseInfo.push(info);\n    return \"(\" + regex + \")\";\n  });\n\n  // Check all the required fields are present\n  Object.keys(requiredFields).forEach(field => {\n    if (!specifiedFields[field]) {\n      throw new Error(\n        `Invalid format. ${field} is required in specified format`\n      );\n    }\n  });\n\n  // Add back all the literals after\n  newFormat = newFormat.replace(/@@@/g, () => literals.shift());\n\n  // Check if the date string matches the format. If it doesn't return null\n  const matches = dateStr.match(new RegExp(newFormat, \"i\"));\n  if (!matches) {\n    return null;\n  }\n\n  const combinedI18nSettings: I18nSettings = assign(\n    assign({}, globalI18n),\n    i18n\n  );\n\n  // For each match, call the parser function for that date part\n  for (let i = 1; i < matches.length; i++) {\n    const [field, , parser] = parseInfo[i - 1];\n    const value = parser\n      ? parser(matches[i], combinedI18nSettings)\n      : +matches[i];\n\n    // If the parser can't make sense of the value, return null\n    if (value == null) {\n      return null;\n    }\n\n    dateInfo[field] = value;\n  }\n\n  if (dateInfo.isPm === 1 && dateInfo.hour != null && +dateInfo.hour !== 12) {\n    dateInfo.hour = +dateInfo.hour + 12;\n  } else if (dateInfo.isPm === 0 && +dateInfo.hour === 12) {\n    dateInfo.hour = 0;\n  }\n\n  let dateTZ: Date;\n  if (dateInfo.timezoneOffset == null) {\n    dateTZ = new Date(\n      dateInfo.year,\n      dateInfo.month,\n      dateInfo.day,\n      dateInfo.hour,\n      dateInfo.minute,\n      dateInfo.second,\n      dateInfo.millisecond\n    );\n    const validateFields: [\n      \"month\" | \"day\" | \"hour\" | \"minute\" | \"second\",\n      \"getMonth\" | \"getDate\" | \"getHours\" | \"getMinutes\" | \"getSeconds\"\n    ][] = [\n      [\"month\", \"getMonth\"],\n      [\"day\", \"getDate\"],\n      [\"hour\", \"getHours\"],\n      [\"minute\", \"getMinutes\"],\n      [\"second\", \"getSeconds\"]\n    ];\n    for (let i = 0, len = validateFields.length; i < len; i++) {\n      // Check to make sure the date field is within the allowed range. Javascript dates allows values\n      // outside the allowed range. If the values don't match the value was invalid\n      if (\n        specifiedFields[validateFields[i][0]] &&\n        dateInfo[validateFields[i][0]] !== dateTZ[validateFields[i][1]]()\n      ) {\n        return null;\n      }\n    }\n  } else {\n    dateTZ = new Date(\n      Date.UTC(\n        dateInfo.year,\n        dateInfo.month,\n        dateInfo.day,\n        dateInfo.hour,\n        dateInfo.minute - dateInfo.timezoneOffset,\n        dateInfo.second,\n        dateInfo.millisecond\n      )\n    );\n\n    // We can't validate dates in another timezone unfortunately. Do a basic check instead\n    if (\n      dateInfo.month > 11 ||\n      dateInfo.month < 0 ||\n      dateInfo.day > 31 ||\n      dateInfo.day < 1 ||\n      dateInfo.hour > 23 ||\n      dateInfo.hour < 0 ||\n      dateInfo.minute > 59 ||\n      dateInfo.minute < 0 ||\n      dateInfo.second > 59 ||\n      dateInfo.second < 0\n    ) {\n      return null;\n    }\n  }\n\n  // Don't allow invalid dates\n\n  return dateTZ;\n}\nexport default {\n  format,\n  parse,\n  defaultI18n,\n  setGlobalDateI18n,\n  setGlobalDateMasks\n};\nexport { format, parse, defaultI18n, setGlobalDateI18n, setGlobalDateMasks };\n"], "names": ["token", "word", "literal", "shorten", "arr", "sLen", "newArr", "i", "len", "length", "push", "substr", "monthUpdate", "arrName", "v", "i18n", "index", "map", "toLowerCase", "indexOf", "assign", "origObj", "_i", "args", "args_1", "_a", "obj", "key", "dayNames", "monthNames", "monthNamesShort", "defaultI18n", "dayNamesShort", "amPm", "DoFn", "dayOfMonth", "globalI18n", "setGlobalDateI18n", "regexEscape", "str", "replace", "pad", "val", "String", "formatFlags", "D", "date<PERSON><PERSON>j", "getDate", "DD", "Do", "d", "getDay", "dd", "ddd", "dddd", "M", "getMonth", "MM", "MMM", "MMMM", "YY", "getFullYear", "YYYY", "h", "getHours", "hh", "H", "HH", "m", "getMinutes", "mm", "s", "getSeconds", "ss", "S", "Math", "round", "getMilliseconds", "SS", "SSS", "a", "A", "toUpperCase", "ZZ", "offset", "getTimezoneOffset", "floor", "abs", "Z", "month<PERSON><PERSON>e", "emptyDigits", "emptyWord", "timezoneOffset", "parts", "match", "minutes", "parseInt", "parseFlags", "cent", "Date", "undefined", "globalMasks", "default", "shortDate", "mediumDate", "longDate", "fullDate", "isoDate", "isoDateTime", "shortTime", "mediumTime", "longTime", "setGlobalDateMasks", "masks", "format", "mask", "Object", "prototype", "toString", "call", "isNaN", "getTime", "Error", "literals", "$0", "$1", "combinedI18nSettings", "shift", "parse", "dateStr", "dateInfo", "year", "month", "day", "hour", "minute", "second", "millisecond", "isPm", "parseInfo", "newFormat", "specifiedFields", "requiredFields", "info", "field", "regex", "requiredField", "keys", "for<PERSON>ach", "matches", "RegExp", "dateTZ", "parser", "value", "validateFields", "UTC"], "mappings": "wLAAA,IAAMA,EAAQ,6EAKRC,EAAO,UACPC,EAAU,gBAyChB,SAASC,EAA4BC,EAAQC,GAE3C,IADA,IAAMC,KACGC,EAAI,EAAGC,EAAMJ,EAAIK,OAAQF,EAAIC,EAAKD,IACzCD,EAAOI,KAAKN,EAAIG,GAAGI,OAAO,EAAGN,IAE/B,OAAOC,EAGT,IAAMM,EAAc,SAClBC,GACG,OAAA,SAACC,EAAWC,GACf,IACMC,EADeD,EAAKF,GAASI,IAAI,SAAAH,GAAK,OAAAA,EAAEI,gBACnBC,QAAQL,EAAEI,eACrC,OAAIF,GAAS,EACJA,EAEF,gBAOOI,EAAOC,OAAc,aAAAC,mBAAAA,IAAAC,oBACnC,IAAkB,QAAAC,IAAAC,WAAAA,IAAM,CAAnB,IAAMC,OACT,IAAK,IAAMC,KAAOD,EAEhBL,EAAQM,GAAOD,EAAIC,GAGvB,OAAON,EAGT,IAAMO,GACJ,SACA,SACA,UACA,YACA,WACA,SACA,YAEIC,GACJ,UACA,WACA,QACA,QACA,MACA,OACA,OACA,SACA,YACA,UACA,WACA,YAGIC,EAA0B3B,EAAQ0B,EAAY,GAG9CE,GACJC,cAH0B7B,EAAQyB,EAAU,GAI5CA,WACAE,kBACAD,aACAI,MAAO,KAAM,MACbC,KAAA,SAAKC,GACH,OACEA,GACC,KAAM,KAAM,KAAM,MACjBA,EAAa,GAAK,EACd,GACEA,EAAcA,EAAa,IAAQ,GAAK,EAAI,GAAKA,EAAc,MAKzEC,EAAahB,KAAWW,GACtBM,EAAoB,SAACtB,GACzB,OAACqB,EAAahB,EAAOgB,EAAYrB,IAE7BuB,EAAc,SAACC,GACnB,OAAAA,EAAIC,QAAQ,oBAAqB,SAE7BC,EAAM,SAACC,EAAsBlC,GAEjC,iBAFiCA,KACjCkC,EAAMC,OAAOD,GACNA,EAAIjC,OAASD,GAClBkC,EAAM,IAAMA,EAEd,OAAOA,GAGHE,GAIJC,EAAG,SAACC,GAA0B,OAAAH,OAAOG,EAAQC,YAC7CC,GAAI,SAACF,GAA0B,OAAAL,EAAIK,EAAQC,YAC3CE,GAAI,SAACH,EAAe/B,GAClB,OAAAA,EAAKmB,KAAKY,EAAQC,YACpBG,EAAG,SAACJ,GAA0B,OAAAH,OAAOG,EAAQK,WAC7CC,GAAI,SAACN,GAA0B,OAAAL,EAAIK,EAAQK,WAC3CE,IAAK,SAACP,EAAe/B,GACnB,OAAAA,EAAKiB,cAAcc,EAAQK,WAC7BG,KAAM,SAACR,EAAe/B,GACpB,OAAAA,EAAKa,SAASkB,EAAQK,WACxBI,EAAG,SAACT,GAA0B,OAAAH,OAAOG,EAAQU,WAAa,IAC1DC,GAAI,SAACX,GAA0B,OAAAL,EAAIK,EAAQU,WAAa,IACxDE,IAAK,SAACZ,EAAe/B,GACnB,OAAAA,EAAKe,gBAAgBgB,EAAQU,aAC/BG,KAAM,SAACb,EAAe/B,GACpB,OAAAA,EAAKc,WAAWiB,EAAQU,aAC1BI,GAAI,SAACd,GACH,OAAAL,EAAIE,OAAOG,EAAQe,eAAgB,GAAGlD,OAAO,IAC/CmD,KAAM,SAAChB,GAA0B,OAAAL,EAAIK,EAAQe,cAAe,IAC5DE,EAAG,SAACjB,GAA0B,OAAAH,OAAOG,EAAQkB,WAAa,IAAM,KAChEC,GAAI,SAACnB,GAA0B,OAAAL,EAAIK,EAAQkB,WAAa,IAAM,KAC9DE,EAAG,SAACpB,GAA0B,OAAAH,OAAOG,EAAQkB,aAC7CG,GAAI,SAACrB,GAA0B,OAAAL,EAAIK,EAAQkB,aAC3CI,EAAG,SAACtB,GAA0B,OAAAH,OAAOG,EAAQuB,eAC7CC,GAAI,SAACxB,GAA0B,OAAAL,EAAIK,EAAQuB,eAC3CE,EAAG,SAACzB,GAA0B,OAAAH,OAAOG,EAAQ0B,eAC7CC,GAAI,SAAC3B,GAA0B,OAAAL,EAAIK,EAAQ0B,eAC3CE,EAAG,SAAC5B,GACF,OAAAH,OAAOgC,KAAKC,MAAM9B,EAAQ+B,kBAAoB,OAChDC,GAAI,SAAChC,GACH,OAAAL,EAAIkC,KAAKC,MAAM9B,EAAQ+B,kBAAoB,IAAK,IAClDE,IAAK,SAACjC,GAA0B,OAAAL,EAAIK,EAAQ+B,kBAAmB,IAC/DG,EAAG,SAAClC,EAAe/B,GACjB,OAAA+B,EAAQkB,WAAa,GAAKjD,EAAKkB,KAAK,GAAKlB,EAAKkB,KAAK,IACrDgD,EAAG,SAACnC,EAAe/B,GACjB,OAAA+B,EAAQkB,WAAa,GACjBjD,EAAKkB,KAAK,GAAGiD,cACbnE,EAAKkB,KAAK,GAAGiD,eACnBC,GAAA,SAAGrC,GACD,IAAMsC,EAAStC,EAAQuC,oBACvB,OACGD,EAAS,EAAI,IAAM,KACpB3C,EAAwC,IAApCkC,KAAKW,MAAMX,KAAKY,IAAIH,GAAU,IAAaT,KAAKY,IAAIH,GAAU,GAAK,IAG3EI,EAAA,SAAE1C,GACA,IAAMsC,EAAStC,EAAQuC,oBACvB,OACGD,EAAS,EAAI,IAAM,KACpB3C,EAAIkC,KAAKW,MAAMX,KAAKY,IAAIH,GAAU,IAAK,GACvC,IACA3C,EAAIkC,KAAKY,IAAIH,GAAU,GAAI,KAW3BK,EAAa,SAAC3E,GAAsB,OAACA,EAAI,GACzC4E,GAA0B,KA7MN,WA8MpBC,GAAwB,KAAM1F,GAC9BgC,GACJ,OACAhC,EACA,SAACa,EAAWC,GACV,IAAM2B,EAAM5B,EAAEI,cACd,OAAIwB,IAAQ3B,EAAKkB,KAAK,GACb,EACES,IAAQ3B,EAAKkB,KAAK,GACpB,EAEF,OAGL2D,GACJ,iBACA,4CACA,SAAC9E,GACC,IAAM+E,GAAS/E,EAAI,IAAIgF,MAAM,iBAE7B,GAAID,EAAO,CACT,IAAME,EAAsB,IAAXF,EAAM,GAAUG,SAASH,EAAM,GAAI,IACpD,MAAoB,MAAbA,EAAM,GAAaE,GAAWA,EAGvC,OAAO,IAGLE,GACJpD,GAAI,MA3OoB,WA4OxBG,IAAK,MA3OW,UA4OhBC,IAAK,MA7OmB,UA6OQhD,EAAM,SAACa,GAAsB,OAAAkF,SAASlF,EAAG,MACzEyC,GAAI,QA9OoB,UA8OQkC,GAChChC,IAAK,QA9OW,SA8OSgC,GACzB7B,IACE,OAhPc,SAkPd,SAAC9C,GACC,IACMoF,IAAS,IADH,IAAIC,MACQtC,eAAelD,OAAO,EAAG,GACjD,QAAS,KAAOG,EAAI,GAAKoF,EAAO,EAAIA,GAAQpF,KAGhDiD,GAAI,OAzPoB,eAyPOqC,EAAW,QAC1CnC,IAAK,OAzPW,cAyPQmC,EAAW,QACnClC,GAAI,OA3PoB,WA4PxBC,IAAK,OA3PW,UA4PhBC,GAAI,SA7PoB,WA8PxBE,IAAK,SA7PW,UA8PhBC,GAAI,SA/PoB,WAgQxBE,IAAK,SA/PW,UAgQhBX,MAAO,OA9PU,UA+PjBY,GAAI,cAAe,MAAO,SAAC5D,GAAsB,OAAK,KAAJA,IAClDgE,IAAK,cAlQW,SAkQe,SAAChE,GAAsB,OAAK,IAAJA,IACvDiE,KAAM,cAlQY,UAmQlB7B,EAAGwC,EACHtC,GAAIsC,EACJrC,IAAKsC,EACLrC,KAAMqC,EACNjC,KAAM,QAASzD,EAAMW,EAAY,oBACjC+C,MAAO,QAAS1D,EAAMW,EAAY,eAClCoE,EAAG/C,EACHgD,EAAGhD,EACHkD,GAAIS,EACJJ,EAAGI,GAICS,GACJC,QAAS,2BACTC,UAAW,SACXC,WAAY,cACZC,SAAU,eACVC,SAAU,qBACVC,QAAS,aACTC,YAAa,uBACbC,UAAW,QACXC,WAAY,WACZC,SAAU,gBAENC,EAAqB,SAACC,GAEK,OAAA7F,EAAOiF,EAAaY,IAS/CC,EAAS,SACbpE,EACAqE,EACApG,GAMA,gBAPAoG,EAAed,EAAqB,sBACpCtF,MAEuB,iBAAZ+B,IACTA,EAAU,IAAIqD,KAAKrD,IAIyB,kBAA5CsE,OAAOC,UAAUC,SAASC,KAAKzE,IAC/B0E,MAAM1E,EAAQ2E,WAEd,MAAM,IAAIC,MAAM,+BAKlB,IAAMC,KAGNR,GALAA,EAAOd,EAAYc,IAASA,GAKhB3E,QAAQtC,EAAS,SAAS0H,EAAIC,GAExC,OADAF,EAASjH,KAAKmH,GACP,QAGT,IAAMC,EAAqC1G,EACzCA,KAAWgB,GACXrB,GAOF,OAJAoG,EAAOA,EAAK3E,QAAQxC,EAAO,SAAA4H,GACzB,OAAAhF,EAAYgF,GAAI9E,EAASgF,MAGftF,QAAQ,OAAQ,WAAM,OAAAmF,EAASI,WAW7C,SAASC,EACPC,EACAf,EACAnG,GAEA,gBAFAA,MAEsB,iBAAXmG,EACT,MAAM,IAAIQ,MAAM,iCAQlB,GAJAR,EAASb,EAAYa,IAAWA,EAI5Be,EAAQxH,OAAS,IACnB,OAAO,KAIT,IACMyH,GACJC,MAFY,IAAIhC,MAEJtC,cACZuE,MAAO,EACPC,IAAK,EACLC,KAAM,EACNC,OAAQ,EACRC,OAAQ,EACRC,YAAa,EACbC,KAAM,KACN9C,eAAgB,MAEZ+C,KACAhB,KAGFiB,EAAY1B,EAAO1E,QAAQtC,EAAS,SAAC0H,EAAIC,GAE3C,OADAF,EAASjH,KAAK4B,EAAYuF,IACnB,QAEHgB,KACAC,KAGNF,EAAYtG,EAAYsG,GAAWpG,QAAQxC,EAAO,SAAA4H,GAChD,IAAMmB,EAAO9C,EAAW2B,GACjBoB,EAAiCD,KAA1BE,EAA0BF,KAAjBG,EAAiBH,KAGxC,GAAIF,EAAgBG,GAClB,MAAM,IAAItB,MAAM,mBAAmBsB,gCAWrC,OARAH,EAAgBG,IAAS,EAGrBE,IACFJ,EAAeI,IAAiB,GAGlCP,EAAUjI,KAAKqI,GACR,IAAME,EAAQ,MAIvB7B,OAAO+B,KAAKL,GAAgBM,QAAQ,SAAAJ,GAClC,IAAKH,EAAgBG,GACnB,MAAM,IAAItB,MACR,mBAAmBsB,wCAMzBJ,EAAYA,EAAUpG,QAAQ,OAAQ,WAAM,OAAAmF,EAASI,UAGrD,IAAMsB,EAAUpB,EAAQnC,MAAM,IAAIwD,OAAOV,EAAW,MACpD,IAAKS,EACH,OAAO,KAST,IANA,IA0BIE,EA1BEzB,EAAqC1G,EACzCA,KAAWgB,GACXrB,GAIOR,EAAI,EAAGA,EAAI8I,EAAQ5I,OAAQF,IAAK,CACjC,IAAAkB,EAAoBkH,EAAUpI,EAAI,GAAjCyI,OAASQ,OACVC,EAAQD,EACVA,EAAOH,EAAQ9I,GAAIuH,IAClBuB,EAAQ9I,GAGb,GAAa,MAATkJ,EACF,OAAO,KAGTvB,EAASc,GAASS,EAUpB,GAPsB,IAAlBvB,EAASQ,MAA+B,MAAjBR,EAASI,MAAmC,KAAlBJ,EAASI,KAC5DJ,EAASI,MAAQJ,EAASI,KAAO,GACN,IAAlBJ,EAASQ,MAAiC,KAAlBR,EAASI,OAC1CJ,EAASI,KAAO,GAIa,MAA3BJ,EAAStC,eAAwB,CACnC2D,EAAS,IAAIpD,KACX+B,EAASC,KACTD,EAASE,MACTF,EAASG,IACTH,EAASI,KACTJ,EAASK,OACTL,EAASM,OACTN,EAASO,aAYX,IAVA,IAAMiB,IAIH,QAAS,aACT,MAAO,YACP,OAAQ,aACR,SAAU,eACV,SAAU,eAEGlJ,GAAPD,EAAI,EAASmJ,EAAejJ,QAAQF,EAAIC,EAAKD,IAGpD,GACEsI,EAAgBa,EAAenJ,GAAG,KAClC2H,EAASwB,EAAenJ,GAAG,MAAQgJ,EAAOG,EAAenJ,GAAG,MAE5D,OAAO,UAiBX,GAbAgJ,EAAS,IAAIpD,KACXA,KAAKwD,IACHzB,EAASC,KACTD,EAASE,MACTF,EAASG,IACTH,EAASI,KACTJ,EAASK,OAASL,EAAStC,eAC3BsC,EAASM,OACTN,EAASO,cAMXP,EAASE,MAAQ,IACjBF,EAASE,MAAQ,GACjBF,EAASG,IAAM,IACfH,EAASG,IAAM,GACfH,EAASI,KAAO,IAChBJ,EAASI,KAAO,GAChBJ,EAASK,OAAS,IAClBL,EAASK,OAAS,GAClBL,EAASM,OAAS,IAClBN,EAASM,OAAS,EAElB,OAAO,KAMX,OAAOe,SAGPrC,SACAc,QACAjG,cACAM,oBACA2E"}