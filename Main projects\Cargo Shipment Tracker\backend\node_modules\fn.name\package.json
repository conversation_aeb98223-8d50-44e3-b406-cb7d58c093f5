{"name": "fn.name", "version": "1.1.0", "description": "Extract names from functions", "main": "index.js", "scripts": {"test": "mocha test.js", "watch": "mocha --watch test.js", "coverage": "istanbul cover ./node_modules/.bin/_mocha -- test.js", "test-travis": "istanbul cover node_modules/.bin/_mocha --report lcovonly -- test.js"}, "repository": {"type": "git", "url": "https://github.com/3rd-Eden/fn.name"}, "keywords": ["fn.name", "function.name", "name", "function", "extract", "parse", "names"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/3rd-Eden/fn.name/issues"}, "devDependencies": {"assume": "2.x.x", "istanbul": "0.3.x", "mocha": "5.x.x", "pre-commit": "1.x.x"}, "homepage": "https://github.com/3rd-Eden/fn.name"}