import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createDrawerNavigator } from '@react-navigation/drawer';
import { createStackNavigator } from '@react-navigation/stack';
import { StyleSheet, Text, View, Platform, Image } from 'react-native';
import { SafeAreaProvider } from "react-native-safe-area-context";
import { Toaster } from 'sonner-native';
import { Ionicons } from '@expo/vector-icons';
import HomeScreen from "./screens/HomeScreen";
import ImageDetailScreen from "./screens/ImageDetailScreen";

// Create stack navigator for screens
const Stack = createStackNavigator();

// Create drawer navigator
const Drawer = createDrawerNavigator();

// Custom drawer content
function CustomDrawerContent() {
  return (
    <View style={styles.drawerHeader}>
      <View style={styles.logoContainer}>
        <Ionicons name="images" size={36} color="#0066cc" />
      </View>
      <Text style={styles.drawerTitle}>Photo Gallery App</Text>
      <Text style={styles.drawerSubtitle}>View and cache photos from Flickr</Text>
    </View>
  );
}

// Stack navigator component to be used inside drawer
function HomeStack() {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="HomeScreen" component={HomeScreen} />
      <Stack.Screen 
        name="ImageDetail" 
        component={ImageDetailScreen}
        options={{
          gestureEnabled: true,
          gestureDirection: 'horizontal',
        }} 
      />
    </Stack.Navigator>
  );
}

export default function App() {
  return (
    <SafeAreaProvider style={styles.container}>
      <Toaster position="top" />
      <NavigationContainer>
        <Drawer.Navigator
          initialRouteName="Home"
          screenOptions={{
            headerShown: false,
            drawerActiveBackgroundColor: '#e6f2ff',
            drawerActiveTintColor: '#0066cc',
            drawerInactiveTintColor: '#333',
            drawerStyle: {
              width: 280,
              backgroundColor: '#ffffff',
            },
            swipeEdgeWidth: Platform.OS === 'android' ? 100 : 20,
          }}
          drawerContent={(props) => (
            <View style={styles.drawerContent}>
              <CustomDrawerContent />
              {props.state.routeNames
                .filter(name => name === "Home")
                .map((name, index) => (
                <View 
                  key={name} 
                  style={[
                    styles.drawerItem,
                    props.state.index === index ? styles.activeDrawerItem : null
                  ]}
                >
                  <Ionicons 
                    name="home" 
                    size={24} 
                    color={props.state.index === index ? '#0066cc' : '#666'} 
                  />
                  <Text 
                    style={[
                      styles.drawerItemText,
                      props.state.index === index ? styles.activeDrawerItemText : null
                    ]}
                    onPress={() => props.navigation.navigate(name)}
                  >
                    {name}
                  </Text>
                </View>
              ))}
              
              <View style={styles.drawerFooter}>
                <Text style={styles.drawerFooterText}>Version 1.0.0</Text>
              </View>
            </View>
          )}
        >
          <Drawer.Screen name="Home" component={HomeStack} />
        </Drawer.Navigator>
      </NavigationContainer>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    userSelect: "none"
  },
  drawerContent: {
    flex: 1,
  },
  drawerHeader: {
    padding: 16,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    alignItems: 'center',
  },
  logoContainer: {
    width: 72,
    height: 72,
    borderRadius: 36,
    backgroundColor: '#e6f2ff',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  drawerTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 6,
    color: '#333',
    textAlign: 'center',
  },
  drawerSubtitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 10,
    paddingHorizontal: 20,
  },
  drawerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 10,
    marginHorizontal: 10,
    marginTop: 8,
  },
  activeDrawerItem: {
    backgroundColor: '#e6f2ff',
  },
  drawerItemText: {
    fontSize: 16,
    marginLeft: 16,
    color: '#666',
  },
  activeDrawerItemText: {
    fontWeight: 'bold',
    color: '#0066cc',
  },
  drawerFooter: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
    marginTop: 'auto',
  },
  drawerFooterText: {
    fontSize: 12,
    color: '#999',
    textAlign: 'center',
  }
});