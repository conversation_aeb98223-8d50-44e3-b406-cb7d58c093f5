// Global variables
let allListings = [];
let shortlistedProperties = [];
let currentUser = null;

// DOM elements
const listingsContainer = document.getElementById('listings-container');
const shortlistContainer = document.getElementById('shortlist-container');
const searchInput = document.getElementById('search-input');
const searchButton = document.getElementById('search-button');
const priceFilter = document.getElementById('price-filter');
const bedroomsFilter = document.getElementById('bedrooms-filter');
const sortFilter = document.getElementById('sort-filter');
const loginForm = document.getElementById('login-form');
const signupForm = document.getElementById('signup-form');

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    // Check if user is logged in
    checkLoginStatus();
    
    // Load shortlisted properties from localStorage
    loadShortlist();
    
    // Check if we're on a page with listings
    if (listingsContainer) {
        // Fetch listings from the backend
        fetchListings();
        
        // Add event listeners for search and filters if elements exist
        if (searchButton && searchInput) {
            searchButton.addEventListener('click', filterListings);
            searchInput.addEventListener('keyup', (e) => {
                if (e.key === 'Enter') {
                    filterListings();
                }
            });
        }
        
        if (priceFilter) priceFilter.addEventListener('change', filterListings);
        if (bedroomsFilter) bedroomsFilter.addEventListener('change', filterListings);
        if (sortFilter) sortFilter.addEventListener('change', filterListings);
    }
    
    // Add event listeners for authentication forms
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
    
    if (signupForm) {
        signupForm.addEventListener('submit', handleSignup);
    }
    
    // Animate stats counters on home page
    animateStatCounters();
});

// Animate stats counters
function animateStatCounters() {
    const propertyCount = document.getElementById('property-count');
    const cityCount = document.getElementById('city-count');
    const agentCount = document.getElementById('agent-count');
    
    if (propertyCount && cityCount && agentCount) {
        // Set target values
        const targetValues = {
            properties: 150,
            cities: 25,
            agents: 40
        };
        
        // Animate counters
        animateCounter(propertyCount, 0, targetValues.properties, 2000);
        animateCounter(cityCount, 0, targetValues.cities, 2000);
        animateCounter(agentCount, 0, targetValues.agents, 2000);
    }
}

// Animate counter from start to end value
function animateCounter(element, start, end, duration) {
    let startTimestamp = null;
    const step = (timestamp) => {
        if (!startTimestamp) startTimestamp = timestamp;
        const progress = Math.min((timestamp - startTimestamp) / duration, 1);
        const value = Math.floor(progress * (end - start) + start);
        element.textContent = value;
        if (progress < 1) {
            window.requestAnimationFrame(step);
        }
    };
    window.requestAnimationFrame(step);
}

// Fetch listings from the backend API
async function fetchListings() {
    try {
        // Show loading state
        listingsContainer.innerHTML = '<div class="loading">Loading properties...</div>';
        
        // Fetch data from the backend
        const response = await fetch('/api/listings');
        
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        // Parse the JSON response
        const data = await response.json();
        
        // Store the listings
        allListings = data;
        
        // Display the listings
        displayListings(allListings);
    } catch (error) {
        console.error('Error fetching listings:', error);
        listingsContainer.innerHTML = `<div class="loading">Error loading properties. Please try again later.</div>`;
    }
}

// Display listings in the listings container
function displayListings(listings) {
    // Clear the container
    listingsContainer.innerHTML = '';
    
    if (listings.length === 0) {
        listingsContainer.innerHTML = '<div class="loading">No properties found matching your criteria.</div>';
        return;
    }
    
    // Create and append listing cards
    listings.forEach(listing => {
        const isShortlisted = shortlistedProperties.some(prop => prop.id === listing.id);
        const card = createPropertyCard(listing, isShortlisted);
        listingsContainer.appendChild(card);
    });
}

// Create a property card element
function createPropertyCard(property, isShortlisted = false) {
    const card = document.createElement('div');
    card.className = 'property-card';
    
    // Format price with commas
    const formattedPrice = property.price.toLocaleString('en-US', {
        style: 'currency',
        currency: 'USD',
        maximumFractionDigits: 0
    });
    
    // Determine bedroom text (studio vs bedrooms)
    const bedroomText = property.bedrooms === 0 ? 'Studio' : `${property.bedrooms} bed${property.bedrooms > 1 ? 's' : ''}`;
    
    // Check if property is liked
    const isLiked = localStorage.getItem(`liked_${property.id}`) === 'true';
    
    card.innerHTML = `
        <div class="property-image" style="background-image: url('${property.image}')">
            <div class="property-price">${formattedPrice}</div>
            <button class="shortlist-btn ${isShortlisted ? 'active' : ''}" data-id="${property.id}">
                <i class="fas fa-heart"></i>
            </button>
        </div>
        <div class="property-details">
            <h3 class="property-title">${property.title}</h3>
            <p class="property-location"><i class="fas fa-map-marker-alt"></i> ${property.location}</p>
            <div class="property-features">
                <div class="feature">
                    <span>${bedroomText}</span>
                    <small>Bedrooms</small>
                </div>
                <div class="feature">
                    <span>${property.bathrooms}</span>
                    <small>Bathrooms</small>
                </div>
                <div class="feature">
                    <span>${property.area}</span>
                    <small>Sq Ft</small>
                </div>
            </div>
            <div class="property-actions">
                <button class="btn-like ${isLiked ? 'active' : ''}" data-id="${property.id}">
                    <i class="fas fa-thumbs-up"></i> Like
                </button>
                <button class="btn-book" data-id="${property.id}">
                    <i class="fas fa-calendar-check"></i> Book Viewing
                </button>
            </div>
        </div>
    `;
    
    // Add event listener to shortlist button
    const shortlistBtn = card.querySelector('.shortlist-btn');
    shortlistBtn.addEventListener('click', () => toggleShortlist(property));
    
    // Add event listener to like button
    const likeBtn = card.querySelector('.btn-like');
    likeBtn.addEventListener('click', () => {
        likeBtn.classList.toggle('active');
        const isNowLiked = likeBtn.classList.contains('active');
        localStorage.setItem(`liked_${property.id}`, isNowLiked);
    });
    
    // Add event listener to book button
    const bookBtn = card.querySelector('.btn-book');
    bookBtn.addEventListener('click', () => {
        if (!currentUser) {
            alert('Please login to book a viewing');
            window.location.href = 'login.html';
            return;
        }
        
        alert(`Booking request sent for ${property.title}. We'll contact you soon to confirm the viewing.`);
    });
    
    return card;
}

// Toggle property in shortlist
function toggleShortlist(property) {
    const index = shortlistedProperties.findIndex(prop => prop.id === property.id);
    
    if (index === -1) {
        // Add to shortlist
        shortlistedProperties.push(property);
    } else {
        // Remove from shortlist
        shortlistedProperties.splice(index, 1);
    }
    
    // Save to localStorage
    saveShortlist();
    
    // Update UI
    updateShortlistUI();
    
    // Update shortlist button in listings
    const shortlistBtn = document.querySelector(`.shortlist-btn[data-id="${property.id}"]`);
    if (shortlistBtn) {
        shortlistBtn.classList.toggle('active');
    }
}

// Save shortlist to localStorage
function saveShortlist() {
    localStorage.setItem('shortlistedProperties', JSON.stringify(shortlistedProperties));
}

// Load shortlist from localStorage
function loadShortlist() {
    const saved = localStorage.getItem('shortlistedProperties');
    if (saved) {
        shortlistedProperties = JSON.parse(saved);
        updateShortlistUI();
    }
}

// Update the shortlist UI
function updateShortlistUI() {
    // Check if shortlist container exists on this page
    if (!shortlistContainer) return;
    
    // Clear the container
    shortlistContainer.innerHTML = '';
    
    if (shortlistedProperties.length === 0) {
        shortlistContainer.innerHTML = '<p class="empty-shortlist">No properties shortlisted yet. Click the heart icon on any property to add it to your shortlist.</p>';
        return;
    }
    
    // Create and append shortlisted property cards
    shortlistedProperties.forEach(property => {
        const card = createPropertyCard(property, true);
        shortlistContainer.appendChild(card);
    });
}

// Filter and sort listings based on user input
function filterListings() {
    const searchTerm = searchInput.value.toLowerCase();
    const priceRange = priceFilter.value;
    const bedroomsValue = bedroomsFilter.value;
    const sortBy = sortFilter.value;
    
    // Filter by search term
    let filtered = allListings.filter(listing => {
        return listing.title.toLowerCase().includes(searchTerm) || 
               listing.location.toLowerCase().includes(searchTerm) ||
               listing.description.toLowerCase().includes(searchTerm);
    });
    
    // Filter by price range
    if (priceRange !== 'all') {
        const [min, max] = priceRange.split('-').map(val => parseInt(val, 10));
        filtered = filtered.filter(listing => {
            if (max) {
                return listing.price >= min && listing.price <= max;
            } else {
                return listing.price >= min;
            }
        });
    }
    
    // Filter by bedrooms
    if (bedroomsValue !== 'all') {
        if (bedroomsValue === '0') {
            // For Studio (0 bedrooms), show only properties with 0 bedrooms
            filtered = filtered.filter(listing => listing.bedrooms === 0);
        } else {
            // For other values, show properties with at least that many bedrooms
            const minBedrooms = parseInt(bedroomsValue, 10);
            filtered = filtered.filter(listing => listing.bedrooms >= minBedrooms);
        }
    }
    
    // Sort listings
    switch (sortBy) {
        case 'price-asc':
            filtered.sort((a, b) => a.price - b.price);
            break;
        case 'price-desc':
            filtered.sort((a, b) => b.price - a.price);
            break;
        case 'bedrooms':
            filtered.sort((a, b) => b.bedrooms - a.bedrooms);
            break;
        case 'area':
            filtered.sort((a, b) => b.area - a.area);
            break;
    }
    
    // Display filtered listings
    displayListings(filtered);
}

// Authentication Functions

// Check if user is logged in
function checkLoginStatus() {
    const user = localStorage.getItem('currentUser');
    if (user) {
        currentUser = JSON.parse(user);
        updateAuthUI(true);
    } else {
        updateAuthUI(false);
    }
}

// Update UI based on authentication status
function updateAuthUI(isLoggedIn) {
    // This function would update UI elements based on login status
    // For example, showing/hiding certain elements or changing text
    console.log('User logged in:', isLoggedIn);
    
    // Update navigation menu
    const loginLink = document.querySelector('nav ul li a[href="login.html"]');
    const signupLink = document.querySelector('nav ul li a[href="signup.html"]');
    
    if (isLoggedIn && loginLink && signupLink) {
        // Replace login link with user profile
        loginLink.textContent = currentUser.name || 'My Account';
        loginLink.href = '#';
        
        // Replace signup link with logout
        signupLink.textContent = 'Logout';
        signupLink.href = '#';
        signupLink.onclick = function(e) {
            e.preventDefault();
            logout();
        };
    }
}

// Handle social login
function socialLogin(provider) {
    // In a real application, this would redirect to OAuth provider
    // For this demo, we'll simulate a successful login
    
    let userData = {};
    
    switch(provider) {
        case 'google':
            userData = {
                name: 'Google User',
                email: '<EMAIL>',
                provider: 'google'
            };
            break;
        case 'facebook':
            userData = {
                name: 'Facebook User',
                email: '<EMAIL>',
                provider: 'facebook'
            };
            break;
        case 'apple':
            userData = {
                name: 'Apple User',
                email: '<EMAIL>',
                provider: 'apple'
            };
            break;
    }
    
    // Store user in localStorage
    localStorage.setItem('currentUser', JSON.stringify(userData));
    currentUser = userData;
    
    // Show success message
    const formResponse = document.getElementById('form-response');
    if (formResponse) {
        showFormMessage(formResponse, `Successfully logged in with ${provider}! Redirecting...`, 'success');
    }
    
    // Redirect to home page after a short delay
    setTimeout(() => {
        window.location.href = 'index.html';
    }, 1500);
}

// Logout function
function logout() {
    localStorage.removeItem('currentUser');
    currentUser = null;
    
    // Redirect to home page
    window.location.href = 'index.html';
}

// Handle login form submission
function handleLogin(e) {
    e.preventDefault();
    
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const formResponse = document.getElementById('form-response');
    
    // In a real application, you would validate credentials against a backend
    // For this demo, we'll simulate a successful login
    
    // Simple validation
    if (!email || !password) {
        showFormMessage(formResponse, 'Please fill in all fields', 'error');
        return;
    }
    
    // Simulate successful login
    const user = {
        email: email,
        name: email.split('@')[0] // Just use part of email as name for demo
    };
    
    // Store user in localStorage
    localStorage.setItem('currentUser', JSON.stringify(user));
    currentUser = user;
    
    // Show success message
    showFormMessage(formResponse, 'Login successful! Redirecting...', 'success');
    
    // Redirect to home page after a short delay
    setTimeout(() => {
        window.location.href = 'index.html';
    }, 1500);
}

// Handle signup form submission
function handleSignup(e) {
    e.preventDefault();
    
    const fullname = document.getElementById('fullname').value;
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm-password').value;
    const formResponse = document.getElementById('form-response');
    
    // Simple validation
    if (!fullname || !email || !password || !confirmPassword) {
        showFormMessage(formResponse, 'Please fill in all fields', 'error');
        return;
    }
    
    if (password !== confirmPassword) {
        showFormMessage(formResponse, 'Passwords do not match', 'error');
        return;
    }
    
    // Simulate successful signup
    const user = {
        name: fullname,
        email: email
    };
    
    // Store user in localStorage
    localStorage.setItem('currentUser', JSON.stringify(user));
    currentUser = user;
    
    // Show success message
    showFormMessage(formResponse, 'Account created successfully! Redirecting...', 'success');
    
    // Redirect to home page after a short delay
    setTimeout(() => {
        window.location.href = 'index.html';
    }, 1500);
}

// Helper function to show form messages
function showFormMessage(element, message, type) {
    element.textContent = message;
    element.classList.remove('hidden', 'success', 'error');
    element.classList.add(type);
}